2025-07-18 16:37:30,645 - asyncio - ERROR - Unclosed connector
connections: ['[(<aiohttp.client_proto.ResponseHandler object at 0x7f73f2f215a0>, 443801.464620576), (<aiohttp.client_proto.ResponseHandler object at 0x7f73e2cc5120>, 443807.735261398)]']
connector: <aiohttp.connector.TCPConnector object at 0x7f740e55f0d0>
2025-07-18 16:40:22,617 - __main__ - WARNING - Slow command execution: watchlist took 6.92s
2025-07-18 16:42:13,120 - handlers.discord.admin.admin_commands - WARNING - Admin fantastic_beagle_96376 cleared 4 messages from #trade (keep_pinned: True)
2025-07-18 16:42:27,797 - handlers.discord.admin.admin_commands - WARNING - Admin fantastic_beagle_96376 cleared 4 messages from #trade-h (keep_pinned: True)
2025-07-18 16:42:50,839 - handlers.discord.admin.admin_commands - WARNING - Admin fantastic_beagle_96376 cleared 2 messages from #📈-market-data (keep_pinned: False)
2025-07-18 16:43:08,885 - __main__ - WARNING - Slow command execution: watchlist took 6.56s
2025-07-18 17:04:16,130 - services.market.mt5_data_service - ERROR - Error fetching real-time price for XAUUSD: Connection timeout to host https://query1.finance.yahoo.com/v8/finance/chart/GC=F
2025-07-18 17:23:43,342 - discord.client - ERROR - Attempting a reconnect in 1.80s
Traceback (most recent call last):
  File "/usr/local/lib/python3.10/dist-packages/discord/client.py", line 659, in connect
    await self.ws.poll_event()
  File "/usr/local/lib/python3.10/dist-packages/discord/gateway.py", line 646, in poll_event
    raise ConnectionClosed(self.socket, shard_id=self.shard_id, code=code) from None
discord.errors.ConnectionClosed: Shard ID None WebSocket closed with 1000
2025-07-19 00:39:57,253 - services.trading.trade_h_service - ERROR - ❌ Error fetching Trade-H account balance: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=39dee21b54ab2e5c3f5122ec79cc75bc1302c9c27147632f43de01d220b17de6
2025-07-19 00:55:09,637 - services.trading.trading_service - ERROR - Network error fetching balance: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=7ac5fdb9cbd6c684c91138571203defc32e66506665b7d2f0e040e931ac33f5b
2025-07-19 00:55:09,638 - services.core.error_service - WARNING - Retry 1/3 for get_account_balance: Network connection issue: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=7ac5fdb9cbd6c684c91138571203defc32e66506665b7d2f0e040e931ac33f5b (Caused by: NetworkError: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=7ac5fdb9cbd6c684c91138571203defc32e66506665b7d2f0e040e931ac33f5b)
2025-07-19 01:44:32,078 - services.trading.trade_h_service - ERROR - ❌ Error fetching Trade-H account balance: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=fc80c793b0f18bcb95c47f5a76465dc500b247fb1c2381572c6205eb09b8a86a
2025-07-19 03:30:24,739 - services.trading.trading_service - ERROR - Network error fetching balance: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=8c29fc3512a3457029efcc072f48ca97d3e6be6907ecd914c7d211650cf47264
2025-07-19 03:30:24,739 - services.core.error_service - WARNING - Retry 1/3 for get_account_balance: Network connection issue: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=8c29fc3512a3457029efcc072f48ca97d3e6be6907ecd914c7d211650cf47264 (Caused by: NetworkError: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=8c29fc3512a3457029efcc072f48ca97d3e6be6907ecd914c7d211650cf47264)
2025-07-19 13:15:01,114 - handlers.discord.admin.admin_commands - WARNING - Admin fantastic_beagle_96376 cleared 5 messages from #paxg (keep_pinned: False)
2025-07-19 13:22:53,647 - handlers.discord.admin.admin_commands - WARNING - Admin fantastic_beagle_96376 cleared 4 messages from #paxg (keep_pinned: False)
2025-07-19 17:44:46,122 - services.market.mt5_data_service - ERROR - Error fetching real-time price for XAUUSD: Connection timeout to host https://query1.finance.yahoo.com/v8/finance/chart/GC=F
2025-07-20 05:19:47,250 - handlers.discord.admin.admin_commands - WARNING - Admin fantastic_beagle_96376 cleared 14 messages from #📝-bot-logs (keep_pinned: True)
2025-07-20 05:26:47,176 - services.market.mt5_data_service - ERROR - Error fetching real-time price for XAUUSD: Connection timeout to host https://query1.finance.yahoo.com/v8/finance/chart/GC=F
2025-07-20 05:42:20,901 - handlers.discord.admin.admin_commands - WARNING - Admin fantastic_beagle_96376 cleared 3 messages from #lam-viec (keep_pinned: True)
2025-07-20 06:23:45,127 - services.market.mt5_data_service - ERROR - Error fetching real-time price for XAUUSD: Connection timeout to host https://query1.finance.yahoo.com/v8/finance/chart/GC=F
2025-07-20 08:12:33,485 - __main__ - WARNING - Slow command execution: watchlist took 8.23s
2025-07-20 08:14:04,684 - handlers.discord.admin.admin_commands - WARNING - Admin fantastic_beagle_96376 cleared 3 messages from #trade-h (keep_pinned: False)
2025-07-20 08:14:16,526 - handlers.discord.admin.admin_commands - WARNING - Admin fantastic_beagle_96376 cleared 3 messages from #trade (keep_pinned: False)
2025-07-20 08:14:21,397 - handlers.discord.trading.advanced_commands - WARNING - Discord HTTP error (attempt 1/3): 404 Not Found (error code: 10008): Unknown Message
2025-07-20 08:14:21,489 - handlers.discord.trading.trade_h_commands - WARNING - Trade-H status message was deleted, recreating...
2025-07-20 08:14:23,712 - handlers.discord.trading.advanced_commands - WARNING - Discord HTTP error (attempt 2/3): 404 Not Found (error code: 10008): Unknown Message
2025-07-20 08:37:29,165 - __main__ - WARNING - Slow command execution: watchlist took 8.92s
2025-07-20 10:03:59,775 - discord.gateway - WARNING - Can't keep up, shard ID None websocket is 42.9s behind.
2025-07-20 13:25:45,293 - discord.client - ERROR - Attempting a reconnect in 0.88s
Traceback (most recent call last):
  File "/usr/local/lib/python3.10/dist-packages/discord/client.py", line 659, in connect
    await self.ws.poll_event()
  File "/usr/local/lib/python3.10/dist-packages/discord/gateway.py", line 646, in poll_event
    raise ConnectionClosed(self.socket, shard_id=self.shard_id, code=code) from None
discord.errors.ConnectionClosed: Shard ID None WebSocket closed with 1000
2025-07-21 01:34:42,719 - discord.gateway - WARNING - Can't keep up, shard ID None websocket is 43.8s behind.
2025-07-21 02:21:46,388 - discord.client - ERROR - Attempting a reconnect in 0.40s
Traceback (most recent call last):
  File "/usr/local/lib/python3.10/dist-packages/discord/client.py", line 659, in connect
    await self.ws.poll_event()
  File "/usr/local/lib/python3.10/dist-packages/discord/gateway.py", line 646, in poll_event
    raise ConnectionClosed(self.socket, shard_id=self.shard_id, code=code) from None
discord.errors.ConnectionClosed: Shard ID None WebSocket closed with 1000
2025-07-21 03:21:54,395 - services.market.market_service - ERROR - [MARKET-SERVICE] CCXT error fetching prices for ['BTCUSDT', 'ETHUSDT', 'BNBUSDT', 'SOLUSDT', 'NEARUSDT', 'LINKUSDT', 'ENAUSDT', 'DOGEUSDT', 'XRPUSDT', 'WLDUSDT']: binance GET https://fapi.binance.com/fapi/v1/ticker/24hr
2025-07-21 03:21:54,396 - services.market.market_service - WARNING - No price data received for watchlist symbols
2025-07-21 04:19:47,364 - services.trading.trading_service - ERROR - Network error fetching balance: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=36c9d13fd1cfe29c03613ed3a221e0dd821259269b32923af776f0c104b5a9f0
2025-07-21 04:19:47,365 - services.core.error_service - WARNING - Retry 1/3 for get_account_balance: Network connection issue: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=36c9d13fd1cfe29c03613ed3a221e0dd821259269b32923af776f0c104b5a9f0 (Caused by: NetworkError: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=36c9d13fd1cfe29c03613ed3a221e0dd821259269b32923af776f0c104b5a9f0)
2025-07-21 07:27:20,447 - discord.gateway - WARNING - Can't keep up, shard ID None websocket is 44.3s behind.
2025-07-21 09:59:00,579 - discord.client - ERROR - Attempting a reconnect in 0.39s
Traceback (most recent call last):
  File "/usr/local/lib/python3.10/dist-packages/discord/client.py", line 659, in connect
    await self.ws.poll_event()
  File "/usr/local/lib/python3.10/dist-packages/discord/gateway.py", line 646, in poll_event
    raise ConnectionClosed(self.socket, shard_id=self.shard_id, code=code) from None
discord.errors.ConnectionClosed: Shard ID None WebSocket closed with 1000
2025-07-21 14:44:21,782 - discord.gateway - WARNING - Can't keep up, shard ID None websocket is 42.4s behind.
2025-07-21 16:47:22,198 - services.market.market_service - ERROR - [MARKET-SERVICE] CCXT error fetching prices for ['BTCUSDT', 'ETHUSDT', 'BNBUSDT', 'SOLUSDT', 'NEARUSDT', 'LINKUSDT', 'ENAUSDT', 'DOGEUSDT', 'XRPUSDT', 'WLDUSDT']: binance GET https://fapi.binance.com/fapi/v1/ticker/24hr
2025-07-21 16:47:22,199 - services.market.market_service - WARNING - No price data received for watchlist symbols
2025-07-21 20:33:07,195 - discord.gateway - WARNING - Can't keep up, shard ID None websocket is 43.9s behind.
2025-07-21 20:42:49,400 - discord.client - ERROR - Attempting a reconnect in 1.51s
Traceback (most recent call last):
  File "/usr/local/lib/python3.10/dist-packages/discord/client.py", line 659, in connect
    await self.ws.poll_event()
  File "/usr/local/lib/python3.10/dist-packages/discord/gateway.py", line 646, in poll_event
    raise ConnectionClosed(self.socket, shard_id=self.shard_id, code=code) from None
discord.errors.ConnectionClosed: Shard ID None WebSocket closed with 1000
2025-07-22 00:06:35,727 - discord.client - ERROR - Attempting a reconnect in 1.88s
Traceback (most recent call last):
  File "/usr/local/lib/python3.10/dist-packages/discord/client.py", line 659, in connect
    await self.ws.poll_event()
  File "/usr/local/lib/python3.10/dist-packages/discord/gateway.py", line 646, in poll_event
    raise ConnectionClosed(self.socket, shard_id=self.shard_id, code=code) from None
discord.errors.ConnectionClosed: Shard ID None WebSocket closed with 1000
2025-07-22 02:06:56,241 - discord.client - ERROR - Attempting a reconnect in 1.37s
Traceback (most recent call last):
  File "/usr/local/lib/python3.10/dist-packages/discord/client.py", line 659, in connect
    await self.ws.poll_event()
  File "/usr/local/lib/python3.10/dist-packages/discord/gateway.py", line 646, in poll_event
    raise ConnectionClosed(self.socket, shard_id=self.shard_id, code=code) from None
discord.errors.ConnectionClosed: Shard ID None WebSocket closed with 1000
2025-07-22 08:17:26,310 - __main__ - WARNING - Slow command execution: watchlist took 9.19s
2025-07-22 08:18:24,467 - asyncio - ERROR - Unclosed connector
connections: ['[(<aiohttp.client_proto.ResponseHandler object at 0x7f7d72be6560>, 759461.432387295)]']
connector: <aiohttp.connector.TCPConnector object at 0x7f7d8fdc6ce0>
2025-07-22 08:20:05,423 - __main__ - WARNING - Slow command execution: watchlist took 8.02s
