# ChartFix Discord Bot

## 📋 Tổng Quan Hệ Thống

ChartFix là một hệ thống bot Discord toàn diện được thiết kế để theo dõi và giao dịch cryptocurrency với các tính năng nâng cao cho phân tích thị trường, tự động hóa giao dịch và cảnh báo thời gian thực.

### 🎯 Mục Tiêu Ch<PERSON>h
- **<PERSON> dõi thị trường**: Giám sát giá cryptocurrency và các chỉ số thị trường 24/7
- **Giao dịch tự động**: Quản lý vị thế và lệnh giao dịch Binance Futures
- **Cảnh báo thông minh**: <PERSON><PERSON> thống thông báo đa dạng với nhiều ngưỡng cảnh báo
- **<PERSON><PERSON> tích kỹ thuật**: Tạo biểu đồ và phân tích chỉ bá<PERSON> kỹ thuật
- **T<PERSON><PERSON> hợp đa nền tảng**: Hỗ trợ Discord và Telegram

## 🏗️ Kiến Trúc Hệ Thống

### Core Components
```
bot.py                    # Main bot instance và event handling
├── handlers/             # Discord command handlers
│   ├── discord/market/   # Market commands (/watchlist, /market, /rates)
│   ├── discord/trading/  # Trading commands (/positions, /tp, /sl)
│   ├── discord/admin/    # Admin commands (/stats, /clear, /pin)
│   └── discord/alerts/   # Alert handlers (price, volume)
├── services/             # Business logic services
│   ├── core/            # Core services (error, symbol, cache)
│   ├── market/          # Market data và monitoring
│   ├── trading/         # Trading operations
│   ├── data/            # Database và caching
│   └── discord/         # Discord utilities
└── utils/               # Shared utilities và constants
```

### Database Schema
- **SQLite Database**: `chartfix.db`
- **Tables**: 
  - `alert_cooldowns`: Quản lý cooldown cho alerts
  - Trading data cache và position tracking

### Configuration Management
- **File**: `config.yaml`
- **Sections**: Discord, Binance, Market, Alerts, Trading
- **Hot-reload**: Hỗ trợ cập nhật config runtime

## 🚀 Tính Năng Hiện Tại

### 1. Market Monitoring
- **Watchlist**: Theo dõi 10 cryptocurrency chính với auto-refresh
- **Market Overview**: Tổng quan thị trường từ CoinGecko
- **P2P Rates**: Giá USDT/VND trên Binance P2P
- **Market Indicators**: BTC Dominance, PAXG, Fear & Greed Index

### 2. Alert Systems
#### Price Alerts
- **Daily Change**: Cảnh báo khi giá thay đổi >5%, >10%, >15%
- **Price Targets**: Cảnh báo khi đạt mức giá đặt trước
- **Short-term**: Cảnh báo 1H và 4H với ngưỡng tùy chỉnh

#### Volume Alerts  
- **Volume Spikes**: Phát hiện volume tăng đột biến (1.8x, 2.2x MA20)
- **Timeframe Support**: H4 và D1 candles
- **Cooldown System**: Tránh spam alerts

#### Market Monitor Alerts
- **Earn Rates**: Cảnh báo lãi suất Binance Earn
- **P2P Rate Changes**: Thông báo thay đổi giá P2P

### 3. Trading Features
#### Position Management
- **Real-time Dashboard**: Hiển thị positions, PnL, orders
- **Dual Account Support**: Main trading + Trade-H account
- **Position Controls**: TP/SL, close positions, close all
- **Auto-refresh**: Cập nhật data mỗi 60 giây

#### Order Management
- **Market Orders**: Mua/bán market với position side
- **Limit Orders**: Đặt lệnh limit với giá chỉ định
- **TP/SL Orders**: Quản lý take profit và stop loss
- **Hedge Mode**: Hỗ trợ LONG/SHORT cùng lúc

### 4. Chart Analysis
- **Technical Charts**: Tạo biểu đồ với EMA, Volume indicators
- **Multiple Timeframes**: 1m, 5m, 15m, 1h, 4h, 1d
- **Chart Types**: Candlestick, line charts
- **Symbol Support**: Tất cả trading pairs trên Binance

### 5. Admin Features
- **Bot Statistics**: Uptime, command usage, error tracking
- **Channel Management**: Pin/unpin messages, clear channels
- **Health Monitoring**: System health checks và error reporting
- **Permission Checks**: Validate bot permissions

## 📱 Discord Commands

### Market Commands
- `/watchlist` - Hiển thị watchlist với auto-refresh
- `/market` - Tổng quan thị trường cryptocurrency  
- `/rates` - Lãi suất Binance Earn và giá P2P USDT/VND
- `/c <symbol>` - Biểu đồ giá cryptocurrency
- `/p <symbol>` - Giá hiện tại của symbol

### Trading Commands  
- `/positions` - Hiển thị tất cả positions hiện tại
- `/tp <symbol> <side> <price>` - Đặt take profit
- `/sl <symbol> <side> <price>` - Đặt stop loss
- `/closeall <confirm>` - Đóng tất cả positions
- `/closeside <side> <confirm>` - Đóng tất cả positions một phía
- `/closepos <symbol> <side> <percentage> <confirm>` - Đóng position cụ thể

### Alert Commands
- `/price_status` - Trạng thái price alerts
- `/volume_status` - Trạng thái volume alerts  
- `/test_volume_alert` - Test volume alert

### Admin Commands
- `/stats` - Thống kê bot và performance
- `/pin <message_id>` - Pin message
- `/unpin <message_id>` - Unpin message
- `/pins` - Liệt kê pinned messages
- `/clear <count> <keep_pinned>` - Xóa messages

## 🔧 Cấu Hình Hệ Thống

### Discord Configuration
```yaml
discord:
  token: "YOUR_BOT_TOKEN"
  guild_id: "YOUR_GUILD_ID" 
  admin_id: "YOUR_ADMIN_ID"
  command_prefix: "!"
```

### Binance API Setup
```yaml
binance:
  api_key: "YOUR_API_KEY"
  api_secret: "YOUR_API_SECRET"
  testnet: false

binance_trade_h:
  api_key: "TRADE_H_API_KEY"
  api_secret: "TRADE_H_API_SECRET"
  channel_name: "trade-h"
  refresh_interval: 60
```

### Market Monitoring
```yaml
market:
  watchlist_symbols:
    - BTCUSDT
    - ETHUSDT
    - BNBUSDT
    # ... more symbols
  price_refresh_interval: 90
  market_overview_refresh_interval: 900
```

### Alert Thresholds
```yaml
price_alerts:
  enabled: true
  daily_change_thresholds: [5, 10, 15]
  price_targets:
    BTCUSDT: [89000, 92000, 96000, 100000]
  short_term_alerts:
    enabled: true
    timeframes:
      1h:
        thresholds: [3, 5, 8]
      4h: 
        thresholds: [4, 7, 10]

volume_alerts:
  enabled: true
  thresholds: [1.8, 2.2]
  ma_period: 20
  monitoring_interval: 300
```

## 🛠️ Cài Đặt và Triển Khai

### Requirements
- Python 3.8+
- Discord Bot Token
- Binance API Keys (với Futures trading permissions)
- SQLite Database

### Installation Steps
1. **Clone Repository**
   ```bash
   git clone <repository-url>
   cd chartfix
   ```

2. **Install Dependencies**
   ```bash
   pip3 install -r requirements.txt
   ```

3. **Configure System**
   - Copy và chỉnh sửa `config.yaml`
   - Thêm Discord bot token
   - Thêm Binance API credentials
   - Cấu hình watchlist symbols

4. **Run Bot**
   ```bash
   python3 bot.py
   ```

5. **Process Management (Production)**
   ```bash
   pm2 start bot.py --name chartfix --interpreter python3
   ```

### Database Setup
- SQLite database tự động tạo khi khởi động
- Backup database định kỳ được khuyến nghị
- Tables sẽ được tạo tự động nếu chưa tồn tại

## 📊 Monitoring và Logging

### Log Files
- `logs/bot.log` - General application logs
- `logs/important.log` - Critical events và errors
- Console output cho real-time monitoring

### Health Checks
- Automatic health monitoring mỗi 5 phút
- Error tracking và recovery mechanisms
- Performance metrics collection

### Statistics Tracking
- Command usage statistics
- API response times
- Error rates và types
- System uptime monitoring

## 🔍 Phân Tích Hệ Thống Hiện Tại

### ✅ Điểm Mạnh
1. **Kiến trúc modular**: Services được tách biệt rõ ràng, dễ maintain
2. **Error handling**: Comprehensive error handling với retry mechanisms
3. **Caching system**: Efficient caching với TTL support
4. **Real-time updates**: Auto-refresh cho watchlist và trading dashboard
5. **Multi-account support**: Hỗ trợ nhiều tài khoản trading
6. **Comprehensive alerts**: Đa dạng loại cảnh báo với cooldown system
7. **Admin tools**: Powerful admin commands cho management
8. **Database integration**: SQLite với connection pooling
9. **Configuration management**: Flexible config với hot-reload
10. **Logging system**: Structured logging với multiple levels

### ⚠️ Điểm Cần Cải Thiện
1. **Code duplication**: Một số logic bị duplicate giữa các services
2. **Memory usage**: Cache không có size limits, có thể memory leak
3. **API rate limiting**: Chưa có sophisticated rate limiting cho external APIs
4. **Error recovery**: Một số services không có graceful recovery
5. **Testing coverage**: Thiếu unit tests và integration tests
6. **Documentation**: Code comments không đầy đủ
7. **Security**: API keys hardcoded trong config file
8. **Scalability**: Single-threaded design có thể bottleneck
9. **Monitoring**: Thiếu metrics collection và alerting
10. **Backup system**: Không có automated backup cho database

### 🚨 Vấn Đề Kỹ Thuật
1. **Threading issues**: Potential race conditions trong cache operations
2. **Resource leaks**: HTTP connections có thể không được cleanup properly
3. **Exception handling**: Một số exceptions không được handle đúng cách
4. **Data consistency**: Không có transaction support cho complex operations
5. **Performance**: Blocking I/O operations có thể slow down bot

## 🚀 Đề Xuất Nâng Cấp

### 1. Cải Thiện Hiệu Suất (Performance)
#### Priority: HIGH
- **Async optimization**: Convert blocking operations sang async/await
- **Connection pooling**: Implement proper HTTP connection pooling
- **Database optimization**: Add indexes, query optimization
- **Memory management**: Implement cache size limits và cleanup
- **Background tasks**: Move heavy operations sang background workers

#### Implementation:
```python
# Example: Async HTTP client với connection pooling
class OptimizedHTTPClient:
    def __init__(self, max_connections=100, timeout=30):
        self.session = aiohttp.ClientSession(
            connector=aiohttp.TCPConnector(limit=max_connections),
            timeout=aiohttp.ClientTimeout(total=timeout)
        )
```

### 2. Tăng Cường Bảo Mật (Security)
#### Priority: HIGH
- **Environment variables**: Move API keys sang environment variables
- **Encryption**: Encrypt sensitive data trong database
- **Rate limiting**: Implement user-based rate limiting
- **Input validation**: Strengthen input validation cho commands
- **Audit logging**: Log tất cả sensitive operations

#### Implementation:
```python
# Example: Secure config management
import os
from cryptography.fernet import Fernet

class SecureConfig:
    def __init__(self):
        self.cipher = Fernet(os.environ.get('ENCRYPTION_KEY'))

    def get_api_key(self, service):
        encrypted_key = self.config[service]['encrypted_key']
        return self.cipher.decrypt(encrypted_key).decode()
```

### 3. Monitoring và Observability
#### Priority: MEDIUM
- **Metrics collection**: Implement Prometheus metrics
- **Health checks**: Advanced health monitoring
- **Performance monitoring**: Track response times, error rates
- **Alerting system**: Alert khi có issues
- **Dashboard**: Grafana dashboard cho monitoring

#### Implementation:
```python
# Example: Metrics collection
from prometheus_client import Counter, Histogram, Gauge

class MetricsCollector:
    def __init__(self):
        self.command_counter = Counter('bot_commands_total', 'Total commands', ['command'])
        self.response_time = Histogram('bot_response_time_seconds', 'Response time')
        self.active_users = Gauge('bot_active_users', 'Active users')
```

### 4. Testing và Quality Assurance
#### Priority: MEDIUM
- **Unit tests**: Comprehensive unit test coverage
- **Integration tests**: Test API integrations
- **Load testing**: Test performance under load
- **Automated testing**: CI/CD pipeline với automated tests
- **Code quality**: Linting, formatting, type hints

#### Implementation:
```python
# Example: Unit test structure
import pytest
from unittest.mock import AsyncMock, patch

class TestMarketService:
    @pytest.mark.asyncio
    async def test_fetch_price_data(self):
        with patch('services.market.market_service.aiohttp.ClientSession') as mock_session:
            mock_session.return_value.get.return_value.__aenter__.return_value.json = AsyncMock(
                return_value={'price': 50000}
            )

            service = MarketService()
            result = await service.fetch_price_data('BTCUSDT')

            assert result['price'] == 50000
```

### 5. Scalability Improvements
#### Priority: LOW
- **Microservices**: Tách services thành separate processes
- **Message queues**: Redis/RabbitMQ cho async processing
- **Load balancing**: Multiple bot instances
- **Database sharding**: Distribute data across multiple databases
- **Caching layer**: Redis cho distributed caching

#### Implementation:
```python
# Example: Message queue integration
import asyncio
import aioredis

class MessageQueue:
    def __init__(self):
        self.redis = aioredis.from_url("redis://localhost")

    async def publish_alert(self, alert_data):
        await self.redis.lpush("alerts", json.dumps(alert_data))

    async def consume_alerts(self):
        while True:
            alert = await self.redis.brpop("alerts", timeout=1)
            if alert:
                await self.process_alert(json.loads(alert[1]))
```

### 6. Feature Enhancements
#### Priority: MEDIUM
- **Advanced charting**: More technical indicators, custom timeframes
- **Portfolio tracking**: Track portfolio performance
- **Social features**: Share trades, leaderboards
- **Mobile app**: React Native app cho mobile access
- **Voice commands**: Discord voice integration
- **AI integration**: ChatGPT cho market analysis

### 7. DevOps và Deployment
#### Priority: MEDIUM
- **Containerization**: Docker containers cho easy deployment
- **CI/CD pipeline**: Automated testing và deployment
- **Infrastructure as Code**: Terraform cho infrastructure
- **Monitoring stack**: ELK stack cho log analysis
- **Backup automation**: Automated database backups

#### Implementation:
```dockerfile
# Example: Dockerfile
FROM python:3.9-slim

WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .
CMD ["python", "bot.py"]
```

## 📈 Roadmap Phát Triển

### Phase 1: Stability & Performance (1-2 tháng)
- [ ] Fix critical bugs và memory leaks
- [ ] Implement comprehensive error handling
- [ ] Add unit tests cho core functions
- [ ] Optimize database queries
- [ ] Implement proper logging

### Phase 2: Security & Monitoring (2-3 tháng)
- [ ] Move sang environment variables
- [ ] Implement encryption cho sensitive data
- [ ] Add metrics collection
- [ ] Set up monitoring dashboard
- [ ] Implement rate limiting

### Phase 3: Feature Enhancement (3-4 tháng)
- [ ] Advanced charting features
- [ ] Portfolio tracking
- [ ] Mobile app development
- [ ] AI integration
- [ ] Social features

### Phase 4: Scalability (4-6 tháng)
- [ ] Microservices architecture
- [ ] Message queue implementation
- [ ] Load balancing
- [ ] Database optimization
- [ ] Performance tuning

## 🤝 Contributing Guidelines

### Development Setup
1. Fork repository
2. Create feature branch
3. Follow coding standards
4. Add tests cho new features
5. Submit pull request

### Code Standards
- Follow PEP 8 style guide
- Add type hints
- Write comprehensive docstrings
- Include unit tests
- Update documentation

### Testing Requirements
- Unit test coverage >80%
- Integration tests cho API calls
- Performance tests cho critical paths
- Manual testing cho UI changes

## 📞 Support và Maintenance

### Regular Maintenance Tasks
- [ ] Weekly: Check logs cho errors
- [ ] Monthly: Update dependencies
- [ ] Quarterly: Performance review
- [ ] Yearly: Security audit

### Emergency Procedures
1. **Bot Down**: Restart service, check logs
2. **API Issues**: Switch to backup APIs
3. **Database Corruption**: Restore from backup
4. **Security Breach**: Rotate API keys, audit logs

### Contact Information
- **Developer**: [Your Contact Info]
- **Issues**: GitHub Issues
- **Documentation**: Wiki pages
- **Support**: Discord server
